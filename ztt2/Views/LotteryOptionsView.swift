//
//  LotteryOptionsView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖选项菜单组件
 * 显示大转盘、盲盒、刮刮卡三个抽奖道具选项的弹窗
 * 基于ztt1项目的LotteryOptionsView设计，适配ztt2项目
 * 集成完整的会员权限检查和引导订阅功能
 */
struct LotteryOptionsView: View {

    @Binding var isPresented: Bool
    let onWheelSelected: () -> Void
    let onBlindBoxSelected: () -> Void
    let onScratchCardSelected: () -> Void
    let onNavigateToSubscription: (() -> Void)?

    @State private var animationTrigger = false
    @State private var showPermissionAlert = false
    @State private var permissionAlertMessage = ""
    @State private var permissionAlertTitle = ""
    @State private var requiredLevel = ""

    // 添加数据管理器和当前用户状态
    @EnvironmentObject private var dataManager: DataManager
    @State private var currentUser: User?
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 选项菜单卡片
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        Text("lottery.menu.title".localized)
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 20)
                    
                    // 选项列表
                    VStack(spacing: 0) {
                        // 大转盘选项
                        LotteryOptionButton(
                            title: "lottery.menu.wheel".localized,
                            subtitle: "lottery.menu.wheel_description".localized,
                            iconName: "arrow.clockwise.circle.fill",
                            iconColor: Color(hex: "#ff6b6b"),
                            action: {
                                handleWheelSelection()
                            }
                        )
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#f5f5f5"))
                            .frame(height: 1)
                            .padding(.horizontal, 20)
                        
                        // 盲盒选项
                        LotteryOptionButton(
                            title: "lottery.menu.blind_box".localized,
                            subtitle: "lottery.menu.blind_box_description".localized,
                            iconName: "shippingbox.fill",
                            iconColor: Color(hex: "#4ecdc4"),
                            action: {
                                handleBlindBoxSelection()
                            }
                        )
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#f5f5f5"))
                            .frame(height: 1)
                            .padding(.horizontal, 20)
                        
                        // 刮刮卡选项
                        LotteryOptionButton(
                            title: "lottery.menu.scratch_card".localized,
                            subtitle: "lottery.menu.scratch_card_description".localized,
                            iconName: "creditcard.fill",
                            iconColor: Color(hex: "#ffa726"),
                            action: {
                                handleScratchCardSelection()
                            }
                        )
                    }
                    .padding(.bottom, 10)
                }
                .frame(width: 280)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1.5)
                )
                .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
            }
            
            // 权限提醒弹窗（暂时保留结构，后续可扩展）
            if showPermissionAlert {
                LotteryPermissionAlertView(
                    isPresented: $showPermissionAlert,
                    title: permissionAlertTitle,
                    message: permissionAlertMessage,
                    requiredLevel: requiredLevel,
                    onUpgradeAction: {
                        showPermissionAlert = false
                        isPresented = false
                        // 延迟执行导航到订阅页面
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            onNavigateToSubscription?()
                        }
                    },
                    onDismiss: {
                        showPermissionAlert = false
                    }
                )
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                // 刷新当前用户信息
                currentUser = dataManager.currentUser
            } else {
                animationTrigger = false
            }
        }
    }

    // MARK: - Private Methods

    /**
     * 处理大转盘选择
     */
    private func handleWheelSelection() {
        // 检查用户权限 - 初级会员及以上可使用大转盘
        if checkPermission(for: "wheel") {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                isPresented = false
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                onWheelSelected()
            }
        } else {
            // 显示权限提示
            permissionAlertTitle = "需要初级会员权限"
            permissionAlertMessage = "大转盘抽奖功能需要初级会员或高级会员权限，升级会员即可解锁此功能。"
            requiredLevel = "basic" // 初级会员
            showPermissionAlert = true
        }
    }

    /**
     * 处理盲盒选择
     */
    private func handleBlindBoxSelection() {
        // 检查用户权限 - 高级会员可使用盲盒
        if checkPermission(for: "blindbox") {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                isPresented = false
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                onBlindBoxSelected()
            }
        } else {
            // 显示权限提示
            permissionAlertTitle = "需要高级会员权限"
            permissionAlertMessage = "盲盒抽奖功能需要高级会员权限，升级到高级会员即可解锁此功能。"
            requiredLevel = "premium" // 高级会员
            showPermissionAlert = true
        }
    }

    /**
     * 处理刮刮卡选择
     */
    private func handleScratchCardSelection() {
        // 检查用户权限 - 高级会员可使用刮刮卡
        if checkPermission(for: "scratchcard") {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                isPresented = false
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                onScratchCardSelected()
            }
        } else {
            // 显示权限提示
            permissionAlertTitle = "需要高级会员权限"
            permissionAlertMessage = "刮刮卡抽奖功能需要高级会员权限，升级到高级会员即可解锁此功能。"
            requiredLevel = "premium" // 高级会员
            showPermissionAlert = true
        }
    }

    /**
     * 检查用户权限
     */
    private func checkPermission(for feature: String) -> Bool {
        guard let user = currentUser else {
            print("⚠️ 当前用户为空，权限检查失败")
            return false
        }

        // 使用DataManager的权限检查方法
        // 注意：这里传入任意成员即可，因为权限检查主要基于用户的会员等级
        guard let anyMember = dataManager.members.first else {
            print("⚠️ 没有找到任何成员，权限检查失败")
            return false
        }

        let hasPermission = dataManager.canMemberUseFeature(anyMember, feature: feature)
        print("🔐 权限检查 - 功能: \(feature), 用户等级: \(user.subscriptionType), 结果: \(hasPermission)")

        return hasPermission
    }
}

/**
 * 抽奖选项按钮组件
 */
struct LotteryOptionButton: View {
    
    let title: String
    let subtitle: String
    let iconName: String
    let iconColor: Color
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            // 按压触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            action()
        }) {
            HStack(spacing: 16) {
                // 图标
                Image(systemName: iconName)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(iconColor.opacity(0.1))
                    )
                
                // 文字内容
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // 右箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .contentShape(Rectangle())
            .background(
                Rectangle()
                    .fill(isPressed ? Color(hex: "#f8ffe5") : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

/**
 * 抽奖权限提醒弹窗组件
 */
struct LotteryPermissionAlertView: View {
    @Binding var isPresented: Bool
    let title: String
    let message: String
    let requiredLevel: String // "basic" 或 "premium"
    let onUpgradeAction: () -> Void
    let onDismiss: () -> Void

    @State private var animationTrigger = false

    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissAlert()
                }

            // 弹窗内容
            VStack(spacing: 12) {
                // 顶部图标
                Image(systemName: requiredLevel == "premium" ? "crown.fill" : "star.fill")
                    .font(.system(size: 32))
                    .foregroundColor(requiredLevel == "premium" ? Color(hex: "#FFD700") : Color(hex: "#64B5F6"))
                    .padding(.top, 16)

                // 标题
                Text(title)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 16)

                // 描述文字
                Text(message)
                    .font(.system(size: 15))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 8)

                // 会员权益说明
                VStack(spacing: 4) {
                    if requiredLevel == "premium" {
                        Text("🎁 高级会员专享：盲盒、刮刮卡、AI分析")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(Color(hex: "#FFD700"))
                    } else {
                        Text("⭐ 初级会员专享：大转盘、多设备同步")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(Color(hex: "#64B5F6"))
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 12)

                // 按钮组
                HStack(spacing: 12) {
                    // 取消按钮
                    Button(action: {
                        dismissAlert()
                    }) {
                        Text("稍后再说")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .frame(height: 42)
                            .frame(minWidth: 100)
                            .background(
                                RoundedRectangle(cornerRadius: 21)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    }

                    // 升级会员按钮
                    Button(action: {
                        // 触觉反馈
                        let generator = UIImpactFeedbackGenerator(style: .medium)
                        generator.impactOccurred()

                        onUpgradeAction()
                    }) {
                        Text(requiredLevel == "premium" ? "升级高级会员" : "升级初级会员")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)
                            .frame(height: 42)
                            .frame(minWidth: 120)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        requiredLevel == "premium" ? Color(hex: "#F9A825") : Color(hex: "#64B5F6"),
                                        requiredLevel == "premium" ? Color(hex: "#FFD700") : Color(hex: "#2196F3")
                                    ]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                                .clipShape(RoundedRectangle(cornerRadius: 21))
                            )
                            .shadow(color: Color.black.opacity(0.15), radius: 5, x: 0, y: 2)
                    }
                }
                .padding(.bottom, 16)
            }
            .frame(width: 300)
            .background(Color.white)
            .cornerRadius(20)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(Color.gray.opacity(0.1), lineWidth: 1)
            )
            .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
            .scaleEffect(animationTrigger ? 1.0 : 0.85)
            .opacity(animationTrigger ? 1.0 : 0)
        }
        .onAppear {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationTrigger = true
            }
        }
    }

    private func dismissAlert() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            animationTrigger = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            onDismiss()
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()

        LotteryOptionsView(
            isPresented: .constant(true),
            onWheelSelected: {
                print("选择大转盘")
            },
            onBlindBoxSelected: {
                print("选择盲盒")
            },
            onScratchCardSelected: {
                print("选择刮刮卡")
            },
            onNavigateToSubscription: {
                print("导航到订阅页面")
            }
        )
    }
}
